<!--审批步骤用户选择组件-->
<template>
  <div class="j-approval-step-user-selector">
    <div v-if="!approvalSteps || approvalSteps.length === 0" class="empty-state">
      <a-empty description="暂无审批步骤" />
    </div>
    <div v-else class="approval-steps-container">
      <div class="steps-header">
        <a-icon type="audit" class="header-icon" />
        <div class="header-content">
          <div class="header-title">审批流程配置</div>
          <div class="header-subtitle">请为每个审批步骤选择一位审批人员</div>
        </div>
      </div>

      <div class="steps-content">
        <div v-for="(step, index) in approvalSteps" :key="step.roleCode" class="approval-step">
          <div class="step-indicator">
            <div class="step-circle">{{ step.sortOrder }}</div>
            <div v-if="index < approvalSteps.length - 1" class="step-line"></div>
          </div>

          <div class="step-content">
            <div class="step-header">
              <div class="role-info">
                <span class="role-name">{{ step.roleName }}</span>
                <span class="role-desc">第{{ step.sortOrder }}步审批</span>
              </div>
            </div>

            <div class="user-selector">
              <JSelectUserByDeptRole
                v-model:value="step.selectedUsers"
                :roleId="step.roleId"
                :roleCode="step.roleCode"
                :radioSelection="true"
                :placeholder="`请选择${step.roleName}审批人员`"
                @change="handleUserChange(index, $event)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import JSelectUserByDeptRole from './JSelectUserByDeptRole.vue';

  defineOptions({ name: 'JApprovalStepUserSelector' });

  const props = defineProps({
    value: propTypes.array.def([]),
    // 审批步骤数据
    approvalSteps: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['change', 'update:value']);

  // 内部值
  const internalValue = ref([]);

  // 监听外部值变化
  watch(
    () => props.value,
    (newVal) => {
      internalValue.value = newVal || [];
    },
    { immediate: true }
  );

  // 监听审批步骤变化，初始化用户选择
  watch(
    () => props.approvalSteps,
    (newSteps) => {
      if (newSteps && newSteps.length > 0) {
        // 为每个步骤初始化selectedUsers字段
        newSteps.forEach(step => {
          if (!step.selectedUsers) {
            step.selectedUsers = [];
          }
        });

        // 初始化时触发一次change事件，确保表单能收集到初始数据
        emitStepData();
      }
    },
    { immediate: true, deep: true }
  );

  /**
   * 发出步骤数据变化事件
   */
  function emitStepData() {
    if (!props.approvalSteps || props.approvalSteps.length === 0) {
      emit('change', []);
      emit('update:value', []);
      return;
    }

    // 构建完整的审批步骤数据
    const stepData = props.approvalSteps.map(step => ({
      roleId: step.roleId,
      roleCode: step.roleCode,
      roleName: step.roleName,
      sortOrder: step.sortOrder,
      selectedUsers: step.selectedUsers || [],
      selectedUser: step.selectedUser || null,
    }));

    console.log('发出审批步骤数据:', stepData);

    // 发出变化事件
    emit('change', stepData);
    emit('update:value', stepData);
  }

  /**
   * 处理用户选择变化
   */
  function handleUserChange(stepIndex: number, selectedUsers: any) {
    console.log(`步骤${stepIndex}用户选择变化:`, selectedUsers);

    // 更新对应步骤的选中用户
    if (props.approvalSteps && props.approvalSteps[stepIndex]) {
      // 单选模式：selectedUsers可能是字符串或数组
      let singleUserId = null;
      if (typeof selectedUsers === 'string') {
        singleUserId = selectedUsers;
      } else if (Array.isArray(selectedUsers) && selectedUsers.length > 0) {
        singleUserId = selectedUsers[0];
      }

      // 保存选中的用户ID
      props.approvalSteps[stepIndex].selectedUsers = singleUserId ? [singleUserId] : [];
      props.approvalSteps[stepIndex].selectedUser = singleUserId;
    }

    // 发出步骤数据变化
    emitStepData();
  }
</script>

<style lang="less" scoped>
  .j-approval-step-user-selector {
    .empty-state {
      text-align: center;
      padding: 40px 0;
      color: #999;
    }

    .approval-steps-container {
      .steps-header {
        margin-bottom: 16px;
        padding: 12px 16px;
        background: #f5f5f5;
        border-radius: 6px;
        border-left: 4px solid #1890ff;

        .header-icon {
          font-size: 16px;
          margin-right: 8px;
          color: #1890ff;
        }

        .header-content {
          display: inline-block;

          .header-title {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
            margin-right: 8px;
          }

          .header-subtitle {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }

      .steps-content {
        .approval-step {
          display: flex;
          margin-bottom: 12px;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;

            .step-indicator .step-line {
              display: none;
            }
          }

          .step-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 12px;
            min-width: 32px;

            .step-circle {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              background: #1890ff;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              font-size: 12px;
            }

            .step-line {
              width: 1px;
              height: 40px;
              background: #d9d9d9;
              margin-top: 4px;
            }
          }

          .step-content {
            flex: 1;
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 12px;

            .step-header {
              margin-bottom: 8px;

              .role-info {
                .role-name {
                  font-size: 14px;
                  font-weight: 600;
                  color: #262626;
                  margin-bottom: 2px;
                }

                .role-desc {
                  font-size: 12px;
                  color: #8c8c8c;
                  background: #f0f0f0;
                  padding: 2px 6px;
                  border-radius: 3px;
                  display: inline-block;
                }
              }
            }

            .user-selector {
              :deep(.ant-select) {
                width: 100%;

                .ant-select-selector {
                  border-radius: 4px;
                  min-height: 32px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
