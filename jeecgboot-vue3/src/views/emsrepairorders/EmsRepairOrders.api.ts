import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/emsrepairorders/emsRepairOrders/list',
  save='/emsrepairorders/emsRepairOrders/add',
  edit='/emsrepairorders/emsRepairOrders/edit',
  deleteOne = '/emsrepairorders/emsRepairOrders/delete',
  deleteBatch = '/emsrepairorders/emsRepairOrders/deleteBatch',
  importExcel = '/emsrepairorders/emsRepairOrders/importExcel',
  exportXls = '/emsrepairorders/emsRepairOrders/exportXls',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param data 提交的数据
 * @param isUpdate 是否为更新操作
 */
export const saveOrUpdate = (data, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  // 新增时使用data传递请求体，编辑时使用params传递参数
  if (isUpdate) {
    return defHttp.post({url: url, params: data});
  } else {
    return defHttp.post({url: url, data: data});
  }
}

/**
 * 检查当前用户所在部门是否有激活的模板
 */
export const checkActiveTemplate = () => {
  return defHttp.get({
    url: '/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates/checkActiveTemplate'
  }, {
    errorMessageMode: 'none' // 禁用自动错误提示，由业务代码处理
  });
}
