<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
      <BasicForm @register="registerForm" name="EmsRepairOrdersForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref, reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {formSchema} from '../EmsRepairOrders.data';
    import {saveOrUpdate, checkActiveTemplate} from '../EmsRepairOrders.api';
    import { useMessage } from '/@/hooks/web/useMessage';
    import { useUserStore } from '/@/store/modules/user';
    import { getDateByPicker } from '/@/utils';
    const { createMessage } = useMessage();
    const userStore = useUserStore();
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const isDetail = ref(false);
    //表单配置
    const [registerForm, { setProps,resetFields, setFieldsValue, validate, scrollToField }] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
    //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        setModalProps({confirmLoading: false,showCancelBtn:!!data?.showFooter,showOkBtn:!!data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        isDetail.value = !!data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
        } else {
            // 新增时设置默认值并获取当前审批模板
            try {
                const templateResult = await checkActiveTemplate();
                console.log('获取到的审批模板:', templateResult);

                // 设置默认值，包括当前用户为发起人
                await setFieldsValue({
                    currentStatus: '1', // 默认状态为审核中
                    reportId: userStore.getUserInfo?.username || '', // 设置当前用户为发起人
                });

                // 只显示审批人员配置字段
                if (templateResult.selectedRoles && templateResult.selectedRoles.length > 0) {
                    setProps({
                        schemas: formSchema.map(schema => {
                            if (schema.field === 'approvalUserConfig') {
                                return {
                                    ...schema,
                                    show: true,
                                    componentProps: {
                                        ...schema.componentProps,
                                        approvalSteps: templateResult.selectedRoles || [],
                                    }
                                };
                            }
                            return schema;
                        })
                    });
                } else {
                    createMessage.warning('当前部门没有激活的审批模板，请联系管理员配置');
                }

            } catch (error) {
                console.error('获取审批模板失败:', error);
                createMessage.error('获取审批模板失败: ' + (error.message || '未知错误'));
                await setFieldsValue({
                    currentStatus: '1',
                    reportId: userStore.getUserInfo?.username || '', // 即使出错也设置当前用户
                });
            }
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //日期个性化选择
    const fieldPickers = reactive({
    });
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
    //表单提交事件
    async function handleSubmit(v) {
        try {
            let values = await validate();
            // 预处理日期数据
            changeDateValue(values);

            // 构造后端期望的DTO数据结构
            let submitData;
            if (isUpdate.value) {
                // 编辑模式：直接提交表单数据
                submitData = values;
            } else {
                // 新增模式：转换为EmsRepairOrderCreateDTO结构
                const { approvalUserConfig, id, ...repairOrderFields } = values;

                console.log('原始表单数据:', values);
                console.log('提取的审批配置:', approvalUserConfig);

                // 构造工单基本信息对象
                const repairOrder = {
                    equipmentId: repairOrderFields.equipmentId,
                    reportId: repairOrderFields.reportId || userStore.getUserInfo?.username,
                    principalId: repairOrderFields.principalId,
                    faultTitle: repairOrderFields.faultTitle,
                    faultDescription: repairOrderFields.faultDescription,
                    attachment: repairOrderFields.attachment,
                    faultAttachment: repairOrderFields.faultAttachment,
                    currentStatus: repairOrderFields.currentStatus || '1',
                    handleImages: repairOrderFields.handleImages,
                    handleAttachment: repairOrderFields.handleAttachment
                };

                // 过滤掉空值字段
                Object.keys(repairOrder).forEach(key => {
                    if (repairOrder[key] === undefined || repairOrder[key] === null) {
                        delete repairOrder[key];
                    }
                });

                // 处理审批配置数据
                let processedApprovalConfig = [];
                if (approvalUserConfig && Array.isArray(approvalUserConfig)) {
                    processedApprovalConfig = approvalUserConfig.filter(step =>
                        step.selectedUsers && step.selectedUsers.length > 0
                    );
                }

                submitData = {
                    repairOrder: repairOrder,
                    approvalUserConfig: processedApprovalConfig
                };

                console.log('最终提交的数据结构:', submitData);
                console.log('审批配置详情:', processedApprovalConfig);
            }

            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(submitData, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } catch (error) {
           console.error('表单提交失败:', error);

           // 处理表单验证错误
           if (error && error.errorFields) {
             const firstField = error.errorFields[0];
             if (firstField) {
               scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
             }
             return Promise.reject(error.errorFields);
           }

           // 处理API调用错误
           if (error && error.message) {
             createMessage.error('提交失败: ' + error.message);
           } else {
             createMessage.error('提交失败，请检查网络连接或联系管理员');
           }

           return Promise.reject(error);
        } finally {
            setModalProps({confirmLoading: false});
        }
    }

    /**
     * 处理日期值
     * @param formData 表单数据
     */
    const changeDateValue = (formData) => {
        if (formData && fieldPickers) {
            for (let key in fieldPickers) {
                if (formData[key]) {
                    formData[key] = getDateByPicker(formData[key], fieldPickers[key]);
                }
            }
        }
    };

</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
